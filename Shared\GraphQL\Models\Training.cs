using Shared.Interfaces;
using Shared.Utils;

namespace Shared.GraphQL.Models;

public class Training : IAuditableEntity
{
    public int Id { get; set; }
    public required string Name { get; set; }
    public string? Description { get; set; }
    public ICollection<Worker> Workers { get; set; } = new List<Worker>();
    public DateTime? StartDate { get; set; }
    public string? Duration { get; set; }
    public string? TrainingType { get; set; }
    public string? Trainer { get; set; }

    // Audit Fields
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string UpdatedBy { get; set; }

    public TrainingDuration? ParsedDuration => Duration != null ? TrainingDuration.Parse(Duration) : null;
}
